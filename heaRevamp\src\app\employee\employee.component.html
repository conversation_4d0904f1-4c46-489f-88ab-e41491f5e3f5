<!-- Header Nav Start -->
<nav class="navbar navbar-expand-md fixed-top ds-hd-nav">
    <div class="container-fluid">
        <div class="ds-hd-left">
            <a class="ds-hd-logo" href="https://www.myhcl.com" target="_blank"><img src="assets/images/hcltech.svg" alt="HCLTech"></a>
            <a class="ds-hd-app" href="index.html">Human Error Avoidance</a>
        </div>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <ul class="ds-hd-right border-none">
                <li class="ds-hd-empsec">
                    <button data-bs-toggle="dropdown" class="btn btn-secondary dropdown-toggle desktop-toggle" type="button" aria-expanded="false" data-bs-auto-close="outside">
                        <span class="ds-hd-sap">51678907 <span class="ds-hd-emp">Employee</span></span>
                        <!-- <span class="ds-hd-name">RV</span> -->
                        <span class="ds-hd-name"><img src="assets/images/user.svg" alt="User Image"></span>
                    </button>
                    <button data-bs-toggle="dropdown" class="btn btn-secondary dropdown-toggle mobile-toggle" type="button" aria-expanded="false" data-bs-auto-close="outside">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end ds-hd-empd">
                        <div class="ds-hd-fullempname">
                            <h5>Rahul Venketwara Narayan</h5>
                            <p>51678907<span class="ds-hd-emprole">Technical Manager</span></p>
                        </div>
                        <h5 class="ds-hd-changerole">Change Role <i class="hdr-changeRole"></i></h5>
                        <ul class="ds-hd-role ds-hd-userole">
                            <li class="active"><a href="#">Employee</a></li>
                            <li><a href="#">Admin</a></li>
                        </ul>
                        <button type="button" data-bs-toggle="modal" data-bs-target="#logoutModal" class="ds-hd-logout">Logout <i class="hdr-logOut"></i></button>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</nav>
<!-- Header Nav End --->
<main>
    <div class="container mt-24">
        <div class="avoidance-sec">
            <p>As we continue to make strides in our rapid growth journey, one of the major challenges impacting the quality of our services to customers is the frequent occurrence of Human Errors and the business impact associated with it.</p>
            <p>To ensure a consistent and uniform understanding of this subject across all stakeholder groups, we have defined Human Error as:<strong> Any deviation from the established practice (Policy/ Process/ Procedure / Instruction) related to, or caused by the action/ activity of an individual in the service life cycle of delivering services.</strong></p>
            <p>In the past, we have undertaken various programs and initiatives to minimize Human Errors and its impact. At this stage of our journey, we have consolidated all the programs and <strong> finalized six (6) levers for Human Error Avoidance (HEA).</strong></p>
            <p>These are:</p>
            <ul>
                <li>Awareness and Training</li>
                <li>Reporting and Compliance</li>
                <li>Operational Process Improvements & Controls</li>
                <li>Technology Controls</li>
                <li>Environmental Improvements</li>
                <li>Technical Skills / Capability - Quality Control in the On-boarding process</li>
            </ul>
            <p>As this program evolves, we will launch more initiatives under the first three levers and subsequently under the additional levers such as Technology Controls, Environmental Improvements and Technical Skills / Capability - Quality Control in the On-boarding process.</p>
            <p>First to help you get familiarized and understand the area of Human Error Avoidance better, we are happy to present e-learning modules on the HEA and Doer-Checker. Further, Human Error Avoidance is a very important area of focus for HCLTech DFS, and we request all team members to treat this as priority to enable its success.<strong> To access the e-learning modules, please login to My HCL-> HR Studio -> Learn-> Human Error Avoidance Program</strong></p>
            <p>For any questions related to Human Error Avoidance, do write to Delivery Assurance DFS at <a href="#"><strong>DeliveryassuranceDFS&#64;hcl.com</strong></a> or use the help section at <a href="#"><strong>http://reporthumanerror.hcl.com</strong></a></p>
            <p><strong>Let us strive towards Zero Defect Delivery through Human Error Avoidance.</strong></p>
            <div class="mt-32 text-center sticky-btn">
                <button data-bs-toggle="modal" data-bs-target="#exampleModal1" class="btn btn-primary btn-ripple ds-btn-primary" type="submit">Human Error Avoidance & Doer-Checker</button>
            </div>
        </div>
    </div>
</main>
<!-- Footer Start -->
<footer class="ds-ft-footer">
    <p class="ds-ft-webspecify">Best viewed on the latest versions of MS Edge, Chrome or Safari.</p>
    <img src="assets/images/hcl.svg" alt="hcl">
</footer>
<!-- Logout Modal -->
<div class="modal ds-modal fade" id="logoutModal" tabindex="-1" aria-labelledby="logoutModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-content">
      <div class="modal-header p-0">
        <button type="button" class="btn-close position-absolute" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="assign-modal-content pl-50 pr-50">
          <div class="text-center mt-24">
              <img src="assets/images/logout.svg" alt="alert" />
              <p class="mt-3">Are you sure you want to logout?</p>
          </div>
      </div>
      <div class="modal-footer border-0 justify-content-center">
        <div class="d-flex">
          <a href="javascript:void(0)" class="btn btn-primary-outline btn-ripple ds-btn-primary-outline me-3" data-bs-dismiss="modal">No</a>
          <a href="common/logout.html" class="btn btn-primary btn-ripple ds-btn-primary">Yes</a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Human alert modal -->
<div class="modal ds-modal fade" id="exampleModal1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">HEA Alert !!</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-12">To download the HEA Compliance of your account, please click on download button.</p>
                <p>Note: The Download of the report would be shared over outlook from Delivery Assurance e-mail ID</p>
            </div>
            <div class="modal-footer border-0 d-flex justify-content-center align-items-center">
                <a href="javascript:void(0)" data-bs-dismiss="modal" class="btn btn-primary btn-ripple ds-btn-primary">Download</a>
            </div>
        </div>
    </div>
</div>
