/* Custom Application Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header specific styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Content area styles */
main {
    min-height: calc(100vh - 200px);
    padding-top: 80px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: #333;
    margin-bottom: 1rem;
}

p {
    color: #555;
    line-height: 1.6;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Button styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    margin: 0.25rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

/* Modal styles */
.modal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.d-flex {
    display: flex;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

.me-3 {
    margin-right: 1rem;
}

.mt-3 {
    margin-top: 1rem;
}

.p-0 {
    padding: 0;
}

.border-0 {
    border: 0;
}

.border-none {
    border: none;
}

.position-absolute {
    position: absolute;
}

.pl-50 {
    padding-left: 3rem;
}

.pr-50 {
    padding-right: 3rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    main {
        padding-top: 70px;
    }
    
    .avoidance-sec {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-lg {
        max-width: calc(100% - 2rem);
    }
    
    .modal-sm {
        max-width: calc(100% - 2rem);
    }
}
