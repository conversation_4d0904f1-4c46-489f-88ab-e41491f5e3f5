/* Design System Styles */
.ds-hd-nav {
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 0.5rem 0;
}

.ds-hd-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ds-hd-logo img {
    height: 40px;
}

.ds-hd-app {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    text-decoration: none;
}

.ds-hd-right {
    list-style: none;
    margin: 0;
    padding: 0;
}

.ds-hd-empsec .dropdown-toggle {
    background: transparent;
    border: none;
    color: #333;
}

.ds-hd-sap {
    margin-right: 0.5rem;
}

.ds-hd-emp {
    font-size: 0.9rem;
    color: #666;
}

.ds-hd-name img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.ds-hd-empd {
    min-width: 250px;
    padding: 1rem;
}

.ds-hd-fullempname h5 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
}

.ds-hd-fullempname p {
    margin: 0.25rem 0 0 0;
    font-size: 0.9rem;
    color: #666;
}

.ds-hd-emprole {
    margin-left: 0.5rem;
    font-style: italic;
}

.ds-hd-changerole {
    margin: 1rem 0 0.5rem 0;
    font-size: 1rem;
    color: #333;
}

.ds-hd-role {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ds-hd-role li {
    margin: 0.25rem 0;
}

.ds-hd-role a {
    color: #666;
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    display: block;
    border-radius: 4px;
}

.ds-hd-role li.active a {
    background-color: #007bff;
    color: white;
}

.ds-hd-logout {
    background: transparent;
    border: none;
    color: #dc3545;
    margin-top: 1rem;
    padding: 0.5rem;
    width: 100%;
    text-align: left;
}

.ds-ft-footer {
    background-color: #f8f9fa;
    padding: 2rem 0;
    text-align: center;
    margin-top: 3rem;
}

.ds-ft-webspecify {
    margin: 0 0 1rem 0;
    color: #666;
    font-size: 0.9rem;
}

.ds-ft-footer img {
    height: 30px;
}

.ds-modal .modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.ds-modal .modal-header {
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
}

.ds-modal .modal-body {
    padding: 1.5rem;
}

.ds-modal .modal-footer {
    padding: 1rem 1.5rem;
}

.ds-btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
}

.ds-btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.ds-btn-primary-outline {
    background-color: transparent;
    border: 2px solid #007bff;
    color: #007bff;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
}

.ds-btn-primary-outline:hover {
    background-color: #007bff;
    color: white;
}

.mobile-toggle {
    display: none;
}

.desktop-toggle {
    display: block;
}

@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }
    
    .desktop-toggle {
        display: none;
    }
    
    .mobile-toggle span {
        display: block;
        width: 20px;
        height: 2px;
        background-color: #333;
        margin: 3px 0;
    }
}

.mt-24 {
    margin-top: 6rem;
}

.mt-32 {
    margin-top: 8rem;
}

.mb-12 {
    margin-bottom: 3rem;
}

.avoidance-sec {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    line-height: 1.6;
}

.avoidance-sec p {
    margin-bottom: 1rem;
    color: #333;
}

.avoidance-sec ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.avoidance-sec li {
    margin: 0.5rem 0;
    color: #333;
}

.sticky-btn {
    position: sticky;
    bottom: 2rem;
    z-index: 100;
}

.btn-ripple {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-ripple:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
