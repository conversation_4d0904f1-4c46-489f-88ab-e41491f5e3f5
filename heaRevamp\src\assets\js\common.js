// Common JavaScript functionality
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Handle dropdown auto-close
    $('.dropdown-toggle').on('click', function(e) {
        e.stopPropagation();
    });

    // Handle role change
    $('.ds-hd-role a').on('click', function(e) {
        e.preventDefault();
        $('.ds-hd-role li').removeClass('active');
        $(this).parent().addClass('active');
        
        // Update the displayed role
        var selectedRole = $(this).text();
        $('.ds-hd-emp').text(selectedRole);
        
        // Close dropdown
        $('.dropdown-toggle').dropdown('hide');
    });

    // Handle logout confirmation
    $('#logoutModal .ds-btn-primary').on('click', function(e) {
        e.preventDefault();
        // Add logout logic here
        console.log('Logout confirmed');
        // For demo purposes, just close the modal
        $('#logoutModal').modal('hide');
    });

    // Handle HEA download
    $('#exampleModal1 .ds-btn-primary').on('click', function(e) {
        e.preventDefault();
        // Add download logic here
        console.log('HEA report download initiated');
        
        // Show success message
        alert('HEA Compliance report download has been initiated. You will receive an email shortly.');
        
        // Close modal
        $('#exampleModal1').modal('hide');
    });

    // Add ripple effect to buttons
    $('.btn-ripple').on('click', function(e) {
        var button = $(this);
        var ripple = $('<span class="ripple"></span>');
        
        button.append(ripple);
        
        var x = e.pageX - button.offset().left;
        var y = e.pageY - button.offset().top;
        
        ripple.css({
            left: x + 'px',
            top: y + 'px'
        });
        
        setTimeout(function() {
            ripple.remove();
        }, 600);
    });

    // Handle mobile menu toggle
    $('.mobile-toggle').on('click', function() {
        $('.navbar-collapse').toggleClass('show');
    });

    // Close mobile menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.navbar').length) {
            $('.navbar-collapse').removeClass('show');
        }
    });

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });

    // Add loading state to buttons
    $('.btn').on('click', function() {
        var button = $(this);
        if (!button.hasClass('no-loading')) {
            button.addClass('loading');
            setTimeout(function() {
                button.removeClass('loading');
            }, 2000);
        }
    });
});

// CSS for ripple effect
var rippleCSS = `
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
`;

// Inject CSS
var style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
